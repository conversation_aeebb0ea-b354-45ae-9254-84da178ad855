<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verification Form</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .verification-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            transition: transform 0.3s ease;
        }
        .form-card:hover {
            transform: translateY(-5px);
        }
        .icon-circle {
            width: 80px;
            height: 80px;
            background-color: #e3f2fd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }
        .verification-input {
            letter-spacing: 0.5em;
            text-align: center;
            font-size: 1.2rem;
        }
        .success-message {
            display: none;
        }
        .loading {
            display: none;
        }
        .btn-primary {
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="form-card p-4 p-md-5">
            <div class="text-center mb-4">
                <div class="icon-circle">
                    {% if code_expired %}
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="#dc3545" class="bi bi-clock-history" viewBox="0 0 16 16">
                            <path d="M8.515 1.019A7 7 0 0 0 8 1V0a8 8 0 0 1 .589.022l-.074.997zm2.004.45a7.003 7.003 0 0 0-.985-.299l.219-.976c.383.086.76.2 1.126.342l-.36.933zm1.37.71a7.01 7.01 0 0 0-.439-.27l.493-.87a8.025 8.025 0 0 1 .979.654l-.615.789a6.996 6.996 0 0 0-.418-.302zm1.834 1.79a6.99 6.99 0 0 0-.653-.796l.724-.69c.27.285.52.59.747.91l-.818.576zm.744 1.352a7.08 7.08 0 0 0-.214-.468l.893-.45a7.976 7.976 0 0 1 .45 1.088l-.95.313a7.023 7.023 0 0 0-.179-.483zm.53 2.507a6.991 6.991 0 0 0-.1-1.025l.985-.17c.067.386.106.778.116 1.17l-1.001.025zm-.131 1.538c.033-.17.06-.339.081-.51l.993.123a7.957 7.957 0 0 1-.23 1.155l-.964-.267c.046-.165.086-.332.12-.501zm-.952 2.379c.184-.29.346-.594.486-.908l.914.405c-.16.36-.345.706-.555 1.038l-.845-.535zm-.964 1.205c.122-.122.239-.248.35-.378l.758.653a8.073 8.073 0 0 1-.401.432l-.707-.707z"/>
                            <path d="M8 1a7 7 0 1 0 4.95 11.95l.707.707A8.001 8.001 0 1 1 8 0v1z"/>
                            <path d="M7.5 3a.5.5 0 0 1 .5.5v5.21l3.248 1.856a.5.5 0 0 1-.496.868l-3.5-2A.5.5 0 0 1 7 9V3.5a.5.5 0 0 1 .5-.5z"/>
                        </svg>
                    {% else %}
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="#0d6efd" class="bi bi-shield-lock" viewBox="0 0 16 16">
                            <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                        </svg>
                    {% endif %}
                </div>
                {% if auto_resent %}
                    <h5 class="text-success mb-2">New Code Sent!</h5>
                    <p class="text-muted">Previous code expired. Please enter the NEW 6-digit code sent to your phone.</p>
                {% elif code_resent %}
                    <h5 class="text-info mb-2">Code Resent</h5>
                    <p class="text-muted">Please enter the NEW 6-digit code</p>
                {% elif code_expired %}
                    <h5 class="text-danger mb-2">Code Expired</h5>
                    <p class="text-muted">Your verification code has expired. Please request a new one.</p>
                {% elif code_invalid %}
                    <h5 class="text-warning mb-2">Invalid Code</h5>
                    <p class="text-muted">Please enter the correct 6-digit code</p>
                {% else %}
                    <p class="text-muted">Please enter the 6-digit code</p>
                {% endif %}
            </div>

            {% if messages %}
                <div class="alert alert-danger">
                    {% for message in messages %}
                        <p>{{ message }}</p>
                    {% endfor %}
                </div>
            {% endif %}

            <form id="verificationForm" method="post" action="{% url 'login_with_phone' %}">
                {% csrf_token %}

                <input type="hidden" name="session_name" value="{{ object.session_name }}">

                <!-- Verification Code Input -->
                <div class="mb-4">
                    <label for="code" class="form-label">Verification Code</label>
                    <input
                        type="text"
                        id="code"
                        name="code"
                        class="form-control verification-input"
                        maxlength="6"
                        placeholder="Enter 6-digit code"
                        required>
                    <div class="form-text">Enter the code sent to {{ object.number }}</div>
                </div>

                <!-- 2FA Password Input (Hidden by default) -->
                <div class="mb-4" id="passwordSection" style="{% if needs_2fa %}display: block;{% else %}display: none;{% endif %}">
                    <label for="password" class="form-label">Two-Factor Authentication Password</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-control"
                        placeholder="Enter your 2FA password"
                        {% if needs_2fa %}required{% endif %}>
                    <div class="form-text">Your account has 2FA enabled. Please enter your password.</div>
                </div>

                <!-- Error Messages -->
                <div id="errorAlert" class="alert alert-danger mb-4" role="alert" style="display: none;">
                    <span id="errorMessage">Invalid verification code. Please try again.</span>
                </div>

                <!-- Action Buttons -->
                <button type="submit" class="btn btn-primary w-100 py-2 mb-3">
                    <span class="normal-text">Verify Code</span>
                    <span class="loading" style="display: none;">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Verifying...
                    </span>
                </button>

                <button type="button" id="resendCode" class="btn btn-outline-secondary w-100 py-2">
                    <span class="resend-text">Resend Code</span>
                    <span class="resend-loading" style="display: none;">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Sending...
                    </span>
                </button>
            </form>

            <div class="success-message text-center">
                <div class="icon-circle bg-success-subtle mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" fill="#198754" class="bi bi-check-lg" viewBox="0 0 16 16">
                        <path d="M12.736 3.97a.733.733 0 0 1 1.047 0c.286.289.29.756.01 1.05L7.88 12.01a.733.733 0 0 1-1.065.02L3.217 8.384a.757.757 0 0 1 0-1.06.733.733 0 0 1 1.047 0l3.052 3.093 5.4-6.425a.247.247 0 0 1 .02-.022Z"/>
                    </svg>
                </div>
                <h3 class="text-success mb-2">Verified Successfully!</h3>
                <p class="text-muted">Your phone number has been verified</p>
            </div>
        </div>
    </div>

    <script>
        // Form submission handler
        document.getElementById('verificationForm').addEventListener('submit', function(e) {
            const form = this;
            const codeInput = form.querySelector('input[name="code"]');
            const passwordInput = form.querySelector('input[name="password"]');
            const errorAlert = document.getElementById('errorAlert');
            const submitBtn = form.querySelector('button[type="submit"]');
            const normalText = submitBtn.querySelector('.normal-text');
            const loadingText = submitBtn.querySelector('.loading');

            // Hide error messages
            hideError();

            // Show loading state
            normalText.style.display = 'none';
            loadingText.style.display = 'inline-block';
            submitBtn.disabled = true;

            // Form will submit normally
        });

        // Resend code handler
        document.getElementById('resendCode').addEventListener('click', function(e) {
            e.preventDefault();
            const btn = this;
            const normalText = btn.querySelector('.resend-text');
            const loadingText = btn.querySelector('.resend-loading');
            const sessionName = document.querySelector('input[name="session_name"]').value;

            // Show loading state
            normalText.style.display = 'none';
            loadingText.style.display = 'inline-block';
            btn.disabled = true;

            // Create form data
            const formData = new FormData();
            formData.append('session_name', sessionName);
            formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

            // Send resend request
            fetch('{% url "resend_verification_code" %}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                normalText.style.display = 'inline';
                loadingText.style.display = 'none';
                btn.disabled = false;

                if (data.success) {
                    showSuccess(data.message || 'Verification code resent successfully!');
                } else {
                    showError(data.error || 'Failed to resend verification code');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                normalText.style.display = 'inline';
                loadingText.style.display = 'none';
                btn.disabled = false;
                showError('Network error. Please try again.');
            });
        });

        // Format verification code input to only allow numbers
        document.querySelector('.verification-input').addEventListener('input', function(e) {
            this.value = this.value.replace(/\D/g, '').slice(0, 6);

            // Auto-focus to password field if code is complete and 2FA is visible
            if (this.value.length === 6) {
                const passwordSection = document.getElementById('passwordSection');
                if (passwordSection.style.display !== 'none') {
                    document.getElementById('password').focus();
                }
            }
        });

        // Check for 2FA requirement and code status in messages and template context
        document.addEventListener('DOMContentLoaded', function() {
            // Check if 2FA is already required from template context
            const passwordSection = document.getElementById('passwordSection');
            if (passwordSection.style.display !== 'none') {
                show2FASection();
            }

            // Check for different states
            {% if auto_resent %}
                // Auto-resent - focus on code input and clear it
                const codeInput = document.getElementById('code');
                codeInput.value = '';
                codeInput.focus();
                codeInput.placeholder = 'Enter NEW 6-digit code';

                // Show success message
                showSuccess('🔄 New verification code sent automatically! Please enter the NEW code.');

            {% elif code_resent %}
                // Manual resent - focus on code input
                const codeInput = document.getElementById('code');
                codeInput.value = '';
                codeInput.focus();
                codeInput.placeholder = 'Enter NEW 6-digit code';

            {% elif code_expired %}
                // Code expired - highlight resend button
                const resendBtn = document.getElementById('resendCode');
                resendBtn.classList.add('btn-warning');
                resendBtn.classList.remove('btn-outline-secondary');
                resendBtn.querySelector('.resend-text').innerHTML = '🔄 Get New Code';

                // Auto-focus on resend button
                resendBtn.focus();
            {% endif %}

            // Check if code invalid - focus on code input
            {% if code_invalid %}
                const codeInput = document.getElementById('code');
                codeInput.focus();
                codeInput.select();
            {% endif %}

            // Also check messages for 2FA requirement
            const messages = document.querySelectorAll('.alert p');
            messages.forEach(function(message) {
                if (message.textContent.includes('Two-factor authentication required')) {
                    show2FASection();
                }
            });
        });

        // Utility functions
        function showError(message) {
            const errorAlert = document.getElementById('errorAlert');
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorAlert.style.display = 'block';
        }

        function hideError() {
            const errorAlert = document.getElementById('errorAlert');
            errorAlert.style.display = 'none';
        }

        function showSuccess(message) {
            // Create temporary success alert
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success';
            successAlert.textContent = message;

            const form = document.getElementById('verificationForm');
            form.insertBefore(successAlert, form.firstChild);

            setTimeout(() => {
                successAlert.remove();
            }, 3000);
        }

        function show2FASection() {
            const passwordSection = document.getElementById('passwordSection');
            passwordSection.style.display = 'block';

            // Update submit button text
            const normalText = document.querySelector('.normal-text');
            normalText.textContent = 'Verify Code & Password';
        }
    </script>
</body>
</html>