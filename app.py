import asyncio
from pyrogram import <PERSON><PERSON>
from pyrogram.errors import SessionPass<PERSON><PERSON><PERSON><PERSON>, PhoneCodeInvalid, PhoneCodeExpired

api_id = 21118868
api_hash = "781cb964bc326312b9c77f4c17ddf491"
target_phone = "+998500405165"  # Added + prefix for international format

async def main():
    """Interactive script to send SMS code and verify with user input."""

    client = Client(
        name="Test",
        api_id=api_id,
        api_hash=api_hash
    )

    try:
        await client.connect()

        # Check if already authorized
        if await client.get_me():
            print("✅ Already logged in!")
            user = await client.get_me()
            print(f"Logged in as: {user.first_name} {user.last_name or ''} (@{user.username or 'N/A'})")
            return

    except Exception:
        # Not authorized yet, continue with login process
        pass

    try:
        print(f"📱 Sending SMS code to {target_phone}...")

        # Send verification code
        sent_code = await client.send_code(target_phone)
        print(f"✅ SMS code sent! Code type: {sent_code.type}")

        # Get verification code from user
        while True:
            try:
                code = input("🔢 Enter the verification code you received: ").strip()

                if not code:
                    print("❌ Please enter a valid code")
                    continue

                print("🔄 Verifying code...")

                # Sign in with the code
                await client.sign_in(target_phone, sent_code.phone_code_hash, code)

                print("✅ Successfully logged in!")

                # Get user info
                user = await client.get_me()
                print(f"Welcome: {user.first_name} {user.last_name or ''} (@{user.username or 'N/A'})")
                break

            except PhoneCodeInvalid:
                print("❌ Invalid verification code. Please try again.")
                continue

            except PhoneCodeExpired:
                print("❌ Verification code expired.")

                # Ask if user wants to resend code
                resend = input("🔄 Do you want to resend the verification code? (y/n): ").strip().lower()
                if resend == 'y':
                    print("📱 Resending verification code...")
                    sent_code = await client.send_code(target_phone)
                    print(f"✅ New SMS code sent! Code type: {sent_code.type}")
                    continue
                else:
                    print("👋 Exiting...")
                    break

            except SessionPasswordNeeded:
                print("🔐 Two-factor authentication is enabled.")

                while True:
                    try:
                        password = input("🔑 Enter your 2FA password: ").strip()

                        if not password:
                            print("❌ Please enter your password")
                            continue

                        await client.check_password(password)
                        print("✅ Successfully logged in with 2FA!")

                        # Get user info
                        user = await client.get_me()
                        print(f"Welcome: {user.first_name} {user.last_name or ''} (@{user.username or 'N/A'})")
                        return

                    except Exception as e:
                        print(f"❌ Invalid password: {e}")
                        continue

            except Exception as e:
                print(f"❌ Error during sign in: {e}")
                break

    except Exception as e:
        print(f"❌ Error: {e}")

    finally:
        if client.is_connected:
            await client.disconnect()
            print("🔌 Disconnected from Telegram")

if __name__ == "__main__":
    print("🚀 Starting Telegram authentication...")
    print("=" * 50)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Script interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
