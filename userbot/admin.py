import json
import asyncio
import logging

from django.contrib import admin
from django.db.models import Model
from django.core.cache import cache
from django.http import HttpRequest, HttpResponse
from django.template.response import TemplateResponse
from django.contrib import messages

from unfold.admin import ModelAdmin, TabularInline

from userbot.models import Session, BotUser, ScheduledMessage
from userbot.external.kurigram.manager import KurigramManager

logger = logging.getLogger(__name__)


class SessionAdmin(ModelAdmin):
    """
    Admin class for Session model.
    """
    list_display = ("session_name", "number", "user_id", "is_active")
    list_filter = ("is_active",)
    search_fields = ("session_name", "number", "user_id")
    ordering = ("-id",)

    def get_fields(self, request, obj=None):
        fields = super().get_fields(request, obj)
        if not obj:
            fields = [field for field in fields if field != "is_active"]
        return fields

    def response_add(
        self,
        request: HttpRequest,
        obj: Model,
        post_url_continue: str | None = None,
    ) -> HttpResponse:
        """
        Handle session creation and send verification code using Ku<PERSON>ram
        """
        try:
            # Initialize KurigramManager
            kurigram_manager = KurigramManager(obj)

            # Send verification code
            result = asyncio.run(kurigram_manager.send_verification_code(obj.number))

            if result['success']:
                # Check if already authorized
                if result.get('already_authorized', False):
                    obj.is_active = True
                    obj.save()
                    messages.success(request, f"Session {obj.session_name} is already authorized and active")
                    return super().response_add(request, obj, post_url_continue)

                # Store phone_code_hash in cache for verification
                if result['phone_code_hash']:
                    cache.set(
                        key=f"phone_code_hash_{obj.session_name}",
                        value=result['phone_code_hash'],
                        timeout=60 * 60,  # 1 hour
                    )

                    # Store session object in cache for verification process
                    cache.set(
                        key=f"session_obj_{obj.session_name}",
                        value=obj.id,
                        timeout=60 * 60,  # 1 hour
                    )

                    messages.success(request, f"Verification code sent to {obj.number}")

                    return TemplateResponse(
                        request=request,
                        template="verification.html",
                        context={
                            "object": obj,
                            "phone_code_hash": result['phone_code_hash'],
                        },
                    )
                else:
                    messages.error(request, "No phone code hash received")
                    return super().response_add(request, obj, post_url_continue)
            else:
                messages.error(request, f"Failed to send verification code: {result.get('error', 'Unknown error')}")
                return super().response_add(request, obj, post_url_continue)

        except Exception as e:
            logger.error(f"Error in response_add for session {obj.session_name}: {e}")
            messages.error(request, f"Error sending verification code: {str(e)}")
            return super().response_add(request, obj, post_url_continue)

    def response_change(self, request, obj):
        """
        Handle session updates and provide authentication options
        """
        if not obj.is_active:
            try:
                # Try to check if session is still valid
                kurigram_manager = KurigramManager(obj)
                is_authorized = asyncio.run(kurigram_manager._check_authorization())

                if is_authorized:
                    obj.is_active = True
                    obj.save()
                    messages.success(request, f"Session {obj.session_name} is now active")
                else:
                    messages.info(request, f"Session {obj.session_name} requires re-authentication")

            except Exception as e:
                logger.error(f"Error checking session {obj.session_name}: {e}")
                messages.warning(request, f"Could not verify session status: {str(e)}")

        return super().response_change(request, obj)


class BotUserAdmin(ModelAdmin):
    """
    Admin class for BotUser model.
    """
    list_display = ("user_id", "first_name", "username", "language_code", "is_premium", "created_at")
    list_filter = ("is_bot", "is_premium", "language_code", "created_at")
    search_fields = ("user_id", "username", "first_name", "last_name")
    ordering = ("-created_at",)
    readonly_fields = ("user_id", "created_at", "updated_at")

    fieldsets = (
        ("Asosiy ma'lumotlar", {
            'fields': ('user_id', 'username', 'first_name', 'last_name')
        }),
        ("Qo'shimcha ma'lumotlar", {
            'fields': ('language_code', 'is_bot', 'is_premium')
        }),
        ("Vaqt ma'lumotlari", {
            'fields': ('created_at', 'updated_at')
        }),
    )


class ScheduledMessageAdmin(ModelAdmin):
    """
    Admin class for ScheduledMessage model.
    """
    list_display = ("user_id", "text_preview", "interval_minutes", "is_active", "status", "sent_count", "last_sent_at", "created_at")
    list_filter = ("is_active", "status", "created_at")
    search_fields = ("user_id", "text")
    ordering = ("-created_at",)
    readonly_fields = ("sent_count", "last_sent_at", "celery_task_id", "created_at", "updated_at")

    def text_preview(self, obj):
        """Show first 50 characters of text"""
        return obj.text[:50] + "..." if len(obj.text) > 50 else obj.text
    text_preview.short_description = "Text Preview"

    fieldsets = (
        ("Asosiy ma'lumotlar", {
            'fields': ('user_id', 'text', 'interval_minutes', 'is_active')
        }),
        ("Status ma'lumotlari", {
            'fields': ('status', 'sent_count', 'last_sent_at', 'error_message')
        }),
        ("Texnik ma'lumotlar", {
            'fields': ('celery_task_id', 'created_at', 'updated_at')
        }),
    )


admin.site.register(Session, SessionAdmin)
admin.site.register(BotUser, BotUserAdmin)
admin.site.register(ScheduledMessage, ScheduledMessageAdmin)
