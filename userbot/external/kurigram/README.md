# KurigramManager

KurigramManager - Telegram folder yaratish uchun Python kutubxonasi. <PERSON><PERSON>ram kutubxonasi (Pyrogram asosida) yordamida Telegram chat folderlarini yaratish uchun yuqori darajadagi interfeys taqdim etadi.

## Xususiyatlar

- **Folder Yaratish**: Telegram chat folderlarini yaratish
- **Kengaytirilgan Filtrlash**: Telegram folder filtrlash imkoniyatlarini qo'llab-quvvatlash
- **Django Integratsiyasi**: Django session boshqaruvi bilan muammosiz integratsiya
- **Async Qo'llab-quvvatlash**: Optimal ishlash uchun to'liq async/await qo'llab-quvvatlash
- **Xatoliklarni Boshqarish**: To'liq xatoliklarni boshqarish

## O'rnatish

1. Kurigram kutubxonasini o'rnating:
```bash
pip install kurigram==2.0.106
```

2. KurigramManager allaqachon userbot external paketlariga kiritilgan.

## Tezkor Boshlash

### As<PERSON><PERSON><PERSON>sh

```python
import asyncio
from userbot.models import Session
from userbot.external.kurigram import KurigramManager

async def main():
    # Sessioningizni oling
    session = Session.objects.get(session_name="your_session", is_active=True)

    # Manager yarating
    manager = KurigramManager(session)

    # Yangi folder yarating
    folder_id = await manager.create_folder(
        name="Ish",
        include_groups=True,
        include_channels=True,
        exclude_muted=True,
        icon="💼"
    )
    print(f"Folder yaratildi, ID: {folder_id}")

asyncio.run(main())
```

## API Ma'lumotnomasi

### KurigramManager Klassi

#### Konstruktor
```python
KurigramManager(session: Session)
```

#### Metodlar

##### `create_folder(name, **options) -> int`
Belgilangan parametrlar bilan yangi chat folder yaratadi.

**Parametrlar:**
- `name` (str): Folder nomi (1-12 belgi)
- `icon` (str, ixtiyoriy): Folder ikonkasi/emoji
- `color` (FolderColor, ixtiyoriy): Folder rangi
- `pinned_chats` (List[Union[int, str]], ixtiyoriy): Pin qilinishi kerak bo'lgan chatlar
- `included_chats` (List[Union[int, str]], ixtiyoriy): Kiritilishi kerak bo'lgan chatlar
- `excluded_chats` (List[Union[int, str]], ixtiyoriy): Chiqarilishi kerak bo'lgan chatlar
- `exclude_muted` (bool): Ovozsiz chatlarni chiqarish
- `exclude_read` (bool): O'qilgan chatlarni chiqarish
- `exclude_archived` (bool): Arxivlangan chatlarni chiqarish
- `include_contacts` (bool): Kontaktlarni kiritish
- `include_non_contacts` (bool): Kontakt bo'lmaganlarni kiritish
- `include_bots` (bool): Botlarni kiritish
- `include_groups` (bool): Guruhlarni kiritish
- `include_channels` (bool): Kanallarni kiritish

**Qaytaradi:** `int` - Yaratilgan folder ID raqami

## Misollar

`examples/kurigram_create_folder_example.py` faylida to'liq foydalanish misollarini ko'ring.

## Folder Ranglari

Mavjud folder ranglari:
- `red` - qizil
- `orange` - to'q sariq
- `violet` - binafsha
- `green` - yashil
- `cyan` - ko'k-yashil
- `blue` - ko'k
- `pink` - pushti

## Cheklovlar

- Har bir akkaunt uchun maksimal 10 ta folder (Telegram cheklovi)
- Folder nomlari 1-12 belgi uzunligida bo'lishi kerak
- Ba'zi xususiyatlar Telegram Premium talab qilishi mumkin

## Litsenziya

Ushbu loyiha asosiy userbot loyihasi bilan bir xil litsenziya ostida tarqatiladi.

## Qo'llab-quvvatlash

Muammolar va savollar uchun:
1. Repositoriyadagi mavjud muammolarni tekshiring
2. Batafsil ma'lumot bilan yangi muammo yarating
3. Xato xabarlarini va takrorlash bosqichlarini kiriting

## O'zgarishlar tarixi

### Versiya 1.0.0
- Dastlabki chiqarish
- Folder yaratish funksiyasi
- Django integratsiyasi
- To'liq xatoliklarni boshqarish
