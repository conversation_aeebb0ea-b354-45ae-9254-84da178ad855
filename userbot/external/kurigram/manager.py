"""
KurigramManager - Telegram folder management and authentication using Kurigram library.

This module provides folder creation and authentication functionality for Telegram
using the Kurigram library, which is based on Pyrogram.
"""

import asyncio
import logging
from typing import List, Optional, Union, Dict, Any
from contextlib import asynccontextmanager

from pyrogram import Client
from pyrogram.enums import FolderColor
from pyrogram.errors import BadRequest, SessionPasswordNeeded, FloodWait, PhoneCodeExpired, PhoneCodeInvalid
from pyrogram.types import User, TermsOfService, SentCode, Dialog
from pyrogram.raw.functions.messages import GetDialogFilters
from pyrogram.raw.types import DialogFilter

from userbot.models import Session


logger = logging.getLogger(__name__)


class KurigramManager:
    """
    Manager class for Telegram folder creation and authentication using Kurigram.

    This class provides methods to create Telegram chat folders and handle
    user authentication using the Kurigram library.
    """

    def __init__(self, session: Session):
        """
        Initialize KurigramManager with a session.

        Args:
            session (Session): Django session model instance
        """
        self.session = session
        self._client = None

    @asynccontextmanager
    async def get_client(self):
        """
        Context manager for Kurigram client with proper session handling.

        Yields:
            Client: Authenticated Kurigram client instance
        """
        if not self.session.is_active:
            raise ValueError("Session is not active")

        client = Client(
            name=self.session.session_name,
            api_id=self.session.api_id,
            api_hash=self.session.api_hash,
            password=self.session.two_fa_password if self.session.two_fa_password else None,
        )

        try:
            await client.start()
            logger.info(f"Kurigram client started for session: {self.session.session_name}")
            yield client
        except Exception as e:
            logger.error(f"Failed to start Kurigram client: {e}")
            raise
        finally:
            if client.is_connected:
                await client.stop()
                logger.info(f"Kurigram client stopped for session: {self.session.session_name}")

    async def create_folder(
        self,
        name: str,
        icon: Optional[str] = None,
        color: Optional[FolderColor] = None,
        pinned_chats: Optional[List[Union[int, str]]] = None,
        included_chats: Optional[List[Union[int, str]]] = None,
        excluded_chats: Optional[List[Union[int, str]]] = None,
        exclude_muted: bool = False,
        exclude_read: bool = False,
        exclude_archived: bool = False,
        include_contacts: bool = False,
        include_non_contacts: bool = False,
        include_bots: bool = False,
        include_groups: bool = False,
        include_channels: bool = False
    ) -> int:
        """
        Create a new chat folder.

        Args:
            name (str): Folder name (1-12 characters)
            icon (str, optional): Folder icon/emoji
            color (FolderColor, optional): Folder color
            pinned_chats (List[Union[int, str]], optional): List of chat IDs to pin
            included_chats (List[Union[int, str]], optional): List of chat IDs to include
            excluded_chats (List[Union[int, str]], optional): List of chat IDs to exclude
            exclude_muted (bool): Exclude muted chats
            exclude_read (bool): Exclude read chats
            exclude_archived (bool): Exclude archived chats
            include_contacts (bool): Include contacts
            include_non_contacts (bool): Include non-contacts
            include_bots (bool): Include bots
            include_groups (bool): Include groups
            include_channels (bool): Include channels

        Returns:
            int: Created folder ID

        Raises:
            RPCError: If the request fails
        """
        try:
            async with self.get_client() as client:
                folder_id = await client.create_folder(
                    name=name,
                    icon=icon,
                    color=color,
                    pinned_chats=pinned_chats or [],
                    included_chats=included_chats or [],
                    excluded_chats=excluded_chats or [],
                    exclude_muted=exclude_muted,
                    exclude_read=exclude_read,
                    exclude_archived=exclude_archived,
                    include_contacts=include_contacts,
                    include_non_contacts=include_non_contacts,
                    include_bots=include_bots,
                    include_groups=include_groups,
                    include_channels=include_channels
                )
                logger.info(f"Created folder '{name}' for session: {self.session.session_name}")
                return folder_id
        except Exception as rpc_error:
            if "RPC" in str(type(rpc_error).__name__):
                logger.error(f"RPC error creating folder '{name}': {rpc_error}")
            else:
                logger.error(f"Failed to create folder '{name}': {rpc_error}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating folder '{name}': {e}")
            raise

    async def send_verification_code(self, phone_number: str = None) -> Dict[str, Any]:
        """
        Send verification code to the phone number.

        Args:
            phone_number: Phone number to send code to (optional, uses session.number if not provided)

        Returns:
            Dict with success status and phone_code_hash or error message
        """
        try:
            # Use provided phone_number or session.number
            target_phone = phone_number or self.session.number

            # Create client without phone_number parameter to avoid session conflicts
            client = Client(
                name=self.session.session_name,
                api_id=self.session.api_id,
                api_hash=self.session.api_hash
            )

            try:
                await client.connect()

                # Check if already authorized by trying to get user info
                try:
                    await client.get_me()
                    logger.info(f"Session {self.session.session_name} is already authorized")
                    return {
                        'success': True,
                        'message': 'Session is already authorized',
                        'phone_code_hash': None,
                        'already_authorized': True
                    }
                except Exception:
                    # User is not authorized, proceed with sending code
                    pass

                # Send verification code
                sent_code: SentCode = await client.send_code(target_phone)

                logger.info(f"Verification code sent to {target_phone} for session {self.session.session_name}")
                return {
                    'success': True,
                    'message': f'Verification code sent to {target_phone}',
                    'phone_code_hash': sent_code.phone_code_hash,
                    'already_authorized': False
                }

            finally:
                if client.is_connected:
                    await client.disconnect()

        except Exception as e:
            logger.error(f"Error sending verification code for {self.session.session_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'phone_code_hash': None
            }

    async def verify_code(
        self,
        phone_code: str,
        phone_code_hash: str
    ) -> Optional[Dict[str, Any]]:
        """
        Verify the phone code and complete authentication.

        Args:
            phone_code: The verification code received
            phone_code_hash: The phone code hash from send_verification_code

        Returns:
            Dict with user info and status, None if failed
        """
        try:
            # Create client without phone_number parameter to avoid session conflicts
            client = Client(
                name=self.session.session_name,
                api_id=self.session.api_id,
                api_hash=self.session.api_hash
            )

            try:
                await client.connect()

                # Try to sign in with the code
                result = await client.sign_in(
                    phone_number=self.session.number,
                    phone_code_hash=phone_code_hash,
                    phone_code=phone_code
                )

                if isinstance(result, User):
                    # Successfully authenticated
                    self.session.user_id = result.id
                    self.session.mark_as_active()

                    logger.info(f"Successfully authenticated session {self.session.session_name}")
                    return {
                        "success": True,
                        "user_id": result.id,
                        "first_name": result.first_name,
                        "last_name": result.last_name,
                        "username": result.username,
                        "needs_password": False
                    }

                elif isinstance(result, TermsOfService):
                    # Terms of service need to be accepted
                    logger.info(f"Terms of service need to be accepted for session {self.session.session_name}")
                    return {
                        "success": False,
                        "needs_terms_acceptance": True,
                        "terms": result
                    }

                elif result is False:
                    # Phone number needs to be registered
                    logger.info(f"Phone number needs to be registered for session {self.session.session_name}")
                    return {
                        "success": False,
                        "needs_registration": True
                    }

            except SessionPasswordNeeded:
                # Two-factor authentication required
                logger.info(f"Two-factor authentication required for session {self.session.session_name}")
                return {
                    "success": False,
                    "needs_password": True
                }

            except PhoneCodeExpired:
                # Verification code has expired
                logger.warning(f"Verification code expired for session {self.session.session_name}")
                return {
                    "success": False,
                    "error": "Verification code has expired. Please request a new code.",
                    "code_expired": True
                }

            except PhoneCodeInvalid:
                # Invalid verification code
                logger.warning(f"Invalid verification code for session {self.session.session_name}")
                return {
                    "success": False,
                    "error": "Invalid verification code. Please check and try again.",
                    "code_invalid": True
                }

            finally:
                if client.is_connected:
                    await client.disconnect()

        except Exception as e:
            logger.error(f"Error verifying code for {self.session.session_name}: {e}")
            return None

    async def verify_password(self, password: str) -> Optional[Dict[str, Any]]:
        """
        Verify two-factor authentication password.

        Args:
            password: The 2FA password

        Returns:
            Dict with user info if successful, None otherwise
        """
        try:
            # Create client without phone_number parameter to avoid session conflicts
            client = Client(
                name=self.session.session_name,
                api_id=self.session.api_id,
                api_hash=self.session.api_hash
            )

            try:
                await client.connect()

                # Check password
                user: User = await client.check_password(password)

                # Update session
                self.session.user_id = user.id
                self.session.mark_as_active()

                logger.info(f"Successfully authenticated with password for session {self.session.session_name}")
                return {
                    "success": True,
                    "user_id": user.id,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "username": user.username
                }

            finally:
                if client.is_connected:
                    await client.disconnect()

        except BadRequest as e:
            logger.error(f"Invalid password for session {self.session.session_name}: {e}")
            return {"success": False, "error": "Invalid password"}
        except Exception as e:
            logger.error(f"Error verifying password for {self.session.session_name}: {e}")
            return None

    async def logout(self) -> bool:
        """
        Log out from Telegram and delete the session.

        Returns:
            True if successful, False otherwise
        """
        try:
            async with self.get_client() as client:
                # Log out from Telegram
                await client.log_out()

                # Update session status in database
                self.session.is_active = False
                self.session.save()

                logger.info(f"Successfully logged out session {self.session.session_name}")
                return True

        except Exception as e:
            logger.error(f"Error logging out session {self.session.session_name}: {e}")
            return False

    async def get_auto_folder_chats(self) -> List[Dict[str, Any]]:
        """
        Get all chats from AUTO folder.

        Returns:
            List of chat dictionaries with id, title, and type
        """
        try:
            async with self.get_client() as client:
                # Get all folders
                folders = await client.get_folders()

                auto_folder = None
                for folder in folders:
                    if folder.title.upper() == "AUTO":
                        auto_folder = folder
                        break

                if not auto_folder:
                    logger.warning(f"AUTO folder not found for session {self.session.session_name}")
                    return []

                # Get chats from AUTO folder
                auto_chats = []
                for chat_id in auto_folder.included_chats:
                    try:
                        chat = await client.get_chat(chat_id)
                        auto_chats.append({
                            'id': chat.id,
                            'title': chat.title or f"{chat.first_name or ''} {chat.last_name or ''}".strip(),
                            'type': chat.type.name.lower(),
                            'username': getattr(chat, 'username', None)
                        })
                    except Exception as e:
                        logger.warning(f"Could not get chat info for {chat_id}: {e}")
                        continue

                logger.info(f"Found {len(auto_chats)} chats in AUTO folder for session {self.session.session_name}")
                return auto_chats

        except Exception as e:
            logger.error(f"Error getting AUTO folder chats for {self.session.session_name}: {e}")
            return []

    async def send_message_to_auto_folder(self, message_text: str) -> Dict[str, Any]:
        """
        Send message to all chats in AUTO folder.

        Args:
            message_text: Text message to send

        Returns:
            Dict with results of message sending
        """
        results = {
            'success': [],
            'failed': [],
            'total_chats': 0,
            'sent_count': 0,
            'failed_count': 0
        }

        try:
            # Get AUTO folder chats
            auto_chats = await self.get_auto_folder_chats()
            results['total_chats'] = len(auto_chats)

            if not auto_chats:
                logger.warning(f"No chats found in AUTO folder for session {self.session.session_name}")
                return results

            async with self.get_client() as client:
                for chat in auto_chats:
                    try:
                        # Send message to chat
                        await client.send_message(chat['id'], message_text)

                        results['success'].append({
                            'chat_id': chat['id'],
                            'chat_title': chat['title'],
                            'chat_type': chat['type']
                        })
                        results['sent_count'] += 1

                        logger.info(f"Message sent to {chat['title']} ({chat['id']})")

                        # Small delay to avoid flood limits
                        await asyncio.sleep(1)

                    except FloodWait as e:
                        logger.warning(f"Flood wait for {chat['title']}: {e.value} seconds")
                        await asyncio.sleep(e.value)

                        # Retry after flood wait
                        try:
                            await client.send_message(chat['id'], message_text)
                            results['success'].append({
                                'chat_id': chat['id'],
                                'chat_title': chat['title'],
                                'chat_type': chat['type']
                            })
                            results['sent_count'] += 1
                            logger.info(f"Message sent to {chat['title']} after flood wait")
                        except Exception as retry_e:
                            results['failed'].append({
                                'chat_id': chat['id'],
                                'chat_title': chat['title'],
                                'error': str(retry_e)
                            })
                            results['failed_count'] += 1
                            logger.error(f"Failed to send message to {chat['title']} after retry: {retry_e}")

                    except Exception as e:
                        results['failed'].append({
                            'chat_id': chat['id'],
                            'chat_title': chat['title'],
                            'error': str(e)
                        })
                        results['failed_count'] += 1
                        logger.error(f"Failed to send message to {chat['title']}: {e}")

            logger.info(f"Message sending completed for session {self.session.session_name}. "
                       f"Sent: {results['sent_count']}, Failed: {results['failed_count']}")

            return results

        except Exception as e:
            logger.error(f"Error in send_message_to_auto_folder for {self.session.session_name}: {e}")
            return results
