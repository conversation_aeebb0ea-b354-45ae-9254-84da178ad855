import json
import asyncio
from django.urls import reverse
from django.contrib import messages
from django.core.cache import cache
from django.shortcuts import redirect
from django.utils import timezone
from django.http import JsonResponse
from django.template.response import TemplateResponse

from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

from django_celery_beat.models import PeriodicTask, IntervalSchedule


from userbot.models import Session, ScheduledMessage
from userbot.external.kurigram.manager import KurigramManager


# pylint: disable=E1101
def login_with_phone(request):
    """
    View to login with a phone number using <PERSON><PERSON><PERSON>.
    """
    code = request.POST.get("code")
    password = request.POST.get("password")  # For 2FA
    session_name = request.POST.get("session_name")
    phone_code_hash = cache.get(f"phone_code_hash_{session_name}")

    try:
        session_obj = Session.get_by_session_name(session_name)
    except Session.DoesNotExist:
        messages.error(
            request,
            f"Session {session_name} does not exist",
        )
        url = reverse("admin:userbot_session_changelist")
        return redirect(url)

    # Initialize KurigramManager
    kurigram_manager = <PERSON><PERSON>ramManager(session_obj)

    try:
        # First try to verify the code
        if code and phone_code_hash:
            result = asyncio.run(kurigram_manager.verify_code(code, phone_code_hash))

            if result and result.get("success"):
                # Clear cache after successful authentication
                cache.delete(f"phone_code_hash_{session_name}")
                cache.delete(f"session_obj_{session_name}")

                url = reverse("admin:userbot_session_changelist")
                messages.success(
                    request,
                    f"✅ Session {session_name} verified successfully",
                )
                return redirect(url)
            elif result and result.get("needs_password"):
                # 2FA required - check if password provided
                if password:
                    try:
                        password_result = asyncio.run(kurigram_manager.verify_password(password))
                        if password_result and password_result.get("success"):
                            # Clear cache after successful authentication
                            cache.delete(f"phone_code_hash_{session_name}")
                            cache.delete(f"session_obj_{session_name}")

                            url = reverse("admin:userbot_session_changelist")
                            messages.success(
                                request,
                                f"Session {session_name} verified successfully with 2FA",
                            )
                            return redirect(url)
                        else:
                            messages.error(
                                request,
                                f"Invalid 2FA password for {session_name}. Please try again.",
                            )
                    except Exception as password_error:
                        logger.error(f"2FA password verification error for {session_name}: {password_error}")
                        messages.error(
                            request,
                            f"Error verifying 2FA password for {session_name}: {str(password_error)}",
                        )
                else:
                    # Return to verification page with 2FA requirement
                    try:
                        session_obj = Session.get_by_session_name(session_name)
                        return TemplateResponse(
                            request=request,
                            template="verification.html",
                            context={
                                "object": session_obj,
                                "phone_code_hash": phone_code_hash,
                                "needs_2fa": True,
                            },
                        )
                    except Session.DoesNotExist:
                        messages.error(
                            request,
                            f"Session {session_name} not found for 2FA verification",
                        )
            elif result and result.get("needs_terms_acceptance"):
                messages.error(
                    request,
                    f"Terms of service need to be accepted for {session_name}",
                )
            elif result and result.get("needs_registration"):
                messages.error(
                    request,
                    f"Phone number needs to be registered for {session_name}",
                )
            elif result and result.get("code_expired"):
                # Code expired - offer to resend
                messages.error(
                    request,
                    f"⏰ Verification code expired for {session_name}. Please use the 'Resend Code' button to get a new one.",
                )
                # Return to verification page with resend option
                try:
                    session_obj = Session.get_by_session_name(session_name)
                    return TemplateResponse(
                        request=request,
                        template="verification.html",
                        context={
                            "object": session_obj,
                            "phone_code_hash": phone_code_hash,
                            "code_expired": True,
                        },
                    )
                except Session.DoesNotExist:
                    pass

            elif result and result.get("code_invalid"):
                # Invalid code - allow retry
                messages.error(
                    request,
                    f"❌ Invalid verification code for {session_name}. Please check and try again.",
                )
                # Return to verification page
                try:
                    session_obj = Session.get_by_session_name(session_name)
                    return TemplateResponse(
                        request=request,
                        template="verification.html",
                        context={
                            "object": session_obj,
                            "phone_code_hash": phone_code_hash,
                            "code_invalid": True,
                        },
                    )
                except Session.DoesNotExist:
                    pass

            else:
                messages.error(
                    request,
                    f"❌ Verification failed for {session_name}. Please try again.",
                )
        else:
            messages.error(
                request,
                f"Missing verification code or phone code hash for {session_name}",
            )

    # pylint: disable=broad-exception-caught
    except Exception as exc:
        messages.error(
            request,
            f"Error during verification for {session_name}: {exc}",
        )

    url = reverse("admin:userbot_session_changelist")
    return redirect(url)


def resend_verification_code(request):
    """
    View to resend verification code for a session.
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    session_name = request.POST.get("session_name")

    if not session_name:
        return JsonResponse({'success': False, 'error': 'Session name is required'})

    try:
        session_obj = Session.get_by_session_name(session_name)
    except Session.DoesNotExist:
        return JsonResponse({'success': False, 'error': f'Session {session_name} does not exist'})

    # Initialize KurigramManager
    kurigram_manager = KurigramManager(session_obj)

    try:
        # Send verification code again
        result = asyncio.run(kurigram_manager.send_verification_code(session_obj.number))

        if result['success']:
            # Update cache with new phone_code_hash
            if result['phone_code_hash']:
                cache.set(
                    key=f"phone_code_hash_{session_name}",
                    value=result['phone_code_hash'],
                    timeout=60 * 60,  # 1 hour
                )

                return JsonResponse({
                    'success': True,
                    'message': f'Verification code resent to {session_obj.number}'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'error': 'No phone code hash received'
                })
        else:
            return JsonResponse({
                'success': False,
                'error': result.get('error', 'Failed to resend verification code')
            })

    except Exception as e:
        logger.error(f"Error resending verification code for {session_name}: {e}")
        return JsonResponse({
            'success': False,
            'error': f'Error resending verification code: {str(e)}'
        })


@api_view(['POST'])
def send_auto_message(request):
    """
    API endpoint to send message to AUTO folder chats using Kurigram

    Accepts:
        - user_id: Telegram user ID (integer)
        - text: Message text to send (string)

    Returns:
        - success: boolean
        - message/error: string
        - results: dict with sending results
    """
    try:
        user_id = request.data.get('user_id')
        text = request.data.get('text')

        # Validate input
        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not text:
            return Response({
                'success': False,
                'error': 'text is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_id = int(user_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'user_id must be a valid integer'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get session
        try:
            session_obj = Session.objects.get(user_id=user_id, is_active=True)
        except Session.DoesNotExist:
            return Response({
                'success': False,
                'error': f'No active session found for user_id: {user_id}',
                'results': {}
            }, status=status.HTTP_400_BAD_REQUEST)

        # Initialize KurigramManager and send message
        kurigram_manager = KurigramManager(session_obj)
        result = asyncio.run(kurigram_manager.send_message_to_auto_folder(text))

        # Add success status to result
        result['success'] = result['sent_count'] > 0

        if result['success']:
            return Response({
                'success': True,
                'message': f'Message sent to {result["sent_count"]} chats successfully',
                'results': result
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': 'Failed to send message to any chat',
                'results': result
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





@api_view(['POST'])
def create_scheduled_message(request):
    """
    API endpoint to create a scheduled message task

    Accepts:
        - user_id: Telegram user ID (integer)
        - text: Message text to send (string)
        - interval_minutes: Interval in minutes between sends (integer)

    Returns:
        - success: boolean
        - message/error: string
        - scheduled_message_id: integer (if successful)
        - task_id: string (Celery task ID if successful)
    """
    try:
        user_id = request.data.get('user_id')
        text = request.data.get('text')
        interval_minutes = request.data.get('interval_minutes')

        # Validate input
        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not text:
            return Response({
                'success': False,
                'error': 'text is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not interval_minutes:
            return Response({
                'success': False,
                'error': 'interval_minutes is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user_id = int(user_id)
            interval_minutes = int(interval_minutes)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'user_id and interval_minutes must be valid integers'
            }, status=status.HTTP_400_BAD_REQUEST)

        if interval_minutes < 1:
            return Response({
                'success': False,
                'error': 'interval_minutes must be at least 1'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if session exists and is active
        try:
            session = Session.objects.get(user_id=user_id, is_active=True)
        except Session.DoesNotExist:
            return Response({
                'success': False,
                'error': f'No active session found for user_id: {user_id}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create ScheduledMessage record
        scheduled_message = ScheduledMessage.objects.create(
            user_id=user_id,
            text=text,
            interval_minutes=interval_minutes,
            is_active=True,
            status='pending'
        )

        # Create or get interval schedule
        schedule, created = IntervalSchedule.objects.get_or_create(
            every=interval_minutes,
            period=IntervalSchedule.MINUTES,
        )

        # Create periodic task
        task_name = f"scheduled_message_{scheduled_message.id}_{user_id}"
        periodic_task = PeriodicTask.objects.create(
            interval=schedule,
            name=task_name,
            task='userbot.tasks.send_scheduled_message',
            args=json.dumps([user_id, text, scheduled_message.id]),
            enabled=True,
        )

        # Update scheduled message with task ID
        scheduled_message.celery_task_id = periodic_task.name
        scheduled_message.save()

        # Calculate estimated next run time based on start_time or current time
        from datetime import timedelta
        if periodic_task.start_time:
            next_run_time = periodic_task.start_time
        else:
            next_run_time = timezone.now() + timedelta(minutes=interval_minutes)

        return Response({
            'success': True,
            'message': 'Scheduled message created successfully',
            'scheduled_message_id': scheduled_message.id,
            'task_id': periodic_task.name,
            'interval_minutes': interval_minutes,
            'next_run_time': next_run_time
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def stop_scheduled_message(request):
    """
    API endpoint to stop a scheduled message task

    Accepts:
        - scheduled_message_id: ID of the scheduled message (integer)

    Returns:
        - success: boolean
        - message/error: string
    """
    try:
        scheduled_message_id = request.data.get('scheduled_message_id')

        if not scheduled_message_id:
            return Response({
                'success': False,
                'error': 'scheduled_message_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            scheduled_message_id = int(scheduled_message_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'scheduled_message_id must be a valid integer'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get scheduled message
        try:
            scheduled_message = ScheduledMessage.objects.get(id=scheduled_message_id)
        except ScheduledMessage.DoesNotExist:
            return Response({
                'success': False,
                'error': f'Scheduled message with ID {scheduled_message_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Disable the periodic task
        if scheduled_message.celery_task_id:
            try:
                periodic_task = PeriodicTask.objects.get(name=scheduled_message.celery_task_id)
                periodic_task.enabled = False
                periodic_task.save()
            except PeriodicTask.DoesNotExist:
                pass  # Task might have been deleted manually

        # Update scheduled message status
        scheduled_message.is_active = False
        scheduled_message.status = 'cancelled'
        scheduled_message.save()

        return Response({
            'success': True,
            'message': 'Scheduled message stopped successfully',
            'scheduled_message_id': scheduled_message.id
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def list_scheduled_messages(request):
    """
    API endpoint to list all scheduled messages for a user

    Query parameters:
        - user_id: Telegram user ID (optional, if not provided returns all)
        - is_active: Filter by active status (optional, true/false)

    Returns:
        - success: boolean
        - scheduled_messages: list of scheduled message objects
    """
    try:
        user_id = request.GET.get('user_id')
        is_active = request.GET.get('is_active')

        # Build query
        queryset = ScheduledMessage.objects.all()

        if user_id:
            try:
                user_id = int(user_id)
                queryset = queryset.filter(user_id=user_id)
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'error': 'user_id must be a valid integer'
                }, status=status.HTTP_400_BAD_REQUEST)

        if is_active is not None:
            is_active_bool = is_active.lower() in ['true', '1', 'yes']
            queryset = queryset.filter(is_active=is_active_bool)

        # Serialize data
        scheduled_messages = []
        for msg in queryset.order_by('-created_at'):
            scheduled_messages.append({
                'id': msg.id,
                'user_id': msg.user_id,
                'text': msg.text,
                'interval_minutes': msg.interval_minutes,
                'is_active': msg.is_active,
                'status': msg.status,
                'sent_count': msg.sent_count,
                'last_sent_at': msg.last_sent_at.isoformat() if msg.last_sent_at else None,
                'created_at': msg.created_at.isoformat(),
                'error_message': msg.error_message,
                'celery_task_id': msg.celery_task_id
            })

        return Response({
            'success': True,
            'scheduled_messages': scheduled_messages,
            'count': len(scheduled_messages)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': f'Internal server error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
